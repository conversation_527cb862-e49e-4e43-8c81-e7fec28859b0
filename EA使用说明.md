# ZigZag回调交易EA使用说明

## 概述
这个EA完全按照您的需求开发，基于ZigZag指标的转折点分析和斐波那契回调理论进行自动交易。

## 核心策略原理

### 1. ZigZag转折点识别
- 使用ZigZag指标找到最近的3个转折点（z1、z2、z3）
- z1是最新的转折点，z2是第二个，z3是第三个
- 从第4根K线开始查找转折点（可配置）

### 2. 趋势判断
- 计算d1 = |z1 - z2|（最近两个转折点的差值）
- 计算d2 = |z2 - z3|（第二和第三个转折点的差值）
- d = max(d1, d2)（取较大的差值作为主要波动幅度）
- 根据d的来源确定趋势方向：
  - 如果d来自d1：以z2为起点，z1为终点
  - 如果d来自d2：以z3为起点，z2为终点

### 3. 斐波那契回调系统
**8个档位设置：**
- 第1档：0.0（起点）
- 第2档：0.236（黄金分割）
- 第3档：0.382
- 第4档：0.5（中点）
- 第5档：0.618（黄金分割）
- 第6档：0.786
- 第7档：0.876
- 第8档：1.0（终点）

**价格计算：**
- 上升趋势：回调价格 = 终点价格 - d × 斐波那契比例
- 下降趋势：回调价格 = 终点价格 + d × 斐波那契比例

### 4. 智能入场策略
**初始下单档位判断（基于d值大小）：**
- d > 40：从第2档开始
- 30 < d ≤ 40：从第3档开始
- 20 < d ≤ 30：从第4档开始
- 15 < d ≤ 20：从第5档开始
- 5 < d ≤ 15：从第6档开始
- d ≤ 5：不交易

### 5. 下单条件
**看涨趋势时：**
- 上上上柱或上上柱最低点价格小于对应的斐波那契档位价格
- 上上柱为阳线，上柱为阳线，上上上柱为阴线
- 下多单，止损价为前4柱最低点

**看跌趋势时：**
- 上上上柱或上上柱最高点价格大于对应的斐波那契档位价格
- 上上柱为阴线，上柱为阴线，上上上柱为阳线
- 下空单，止损价为前4柱最高点

### 6. 提前入场功能
- 在价格完全回调到位之前3%入场
- 提高入场成功率

### 7. 持仓保护
- 持仓期间不更新ZigZag转折点
- 防止持仓中途改变交易逻辑
- 平仓后更新ZigZag转折点

### 8. 移动止损功能
- 当新的K线出现时，多单检测上一柱最低点是否盈利1*0.1*d*100*point
- 空单检测上一柱最高点是否盈利1*0.1*d*100*point
- 若是，则修改止损为上一柱最低点-44*point（多单）和上一柱最高点+44*point（空单）

## 参数设置

### ZigZag指标参数
- **ZigZag_Depth**: ZigZag深度（默认12）
- **ZigZag_Deviation**: ZigZag偏差（默认5）
- **ZigZag_Backstep**: ZigZag回溯（默认3）
- **StartBar**: 从第几根K线开始查找转折点（默认4）

### 斐波那契档位设置
- **Fibo_Level1-8**: 8个斐波那契档位（可自定义修改）

### 交易参数
- **EnableTrading**: 启用自动交易（默认true）
- **LotSize**: 交易手数（默认0.1）
- **MagicNumber**: 魔术编号（默认12345）
- **Slippage**: 滑点（默认3）

### d值阈值设置
- **D_Threshold_Level2**: d>40时从第2档开始（默认40.0）
- **D_Threshold_Level3**: 30<d≤40时从第3档开始（默认30.0）
- **D_Threshold_Level4**: 20<d≤30时从第4档开始（默认20.0）
- **D_Threshold_Level5**: 15<d≤20时从第5档开始（默认15.0）
- **D_Threshold_Level6**: 5<d≤15时从第6档开始（默认5.0）
- **D_Min_Trade**: d≤5时不交易（默认5.0）

### 提前入场功能
- **EnableEarlyEntry**: 启用提前入场（默认true）
- **EarlyEntryPercent**: 提前入场百分比（默认3.0%）

### 移动止损功能
- **EnableTrailingStop**: 启用移动止损（默认false）
- **TrailingMultiplier**: 移动止损倍数（默认1.0）
- **TrailingStopPoints**: 移动止损点数（默认44）

### 显示参数
- **ShowInfo**: 显示信息（默认true）
- **InfoColor**: 信息颜色（默认黄色）
- **FontSize**: 字体大小（默认10）

## 安装和使用

1. 将`ZigZag回调交易EA.mq4`文件复制到MT4的`Experts`文件夹
2. 重启MT4或刷新导航器
3. 将EA拖拽到图表上
4. 在弹出的参数设置窗口中调整参数
5. 确保启用自动交易
6. 点击确定开始运行

## 注意事项

1. **回测建议**: 在实盘使用前，请先进行充分的回测
2. **参数调整**: 所有数字设置都可以方便地修改，便于后期回测优化
3. **风险管理**: 请根据自己的风险承受能力设置合适的手数
4. **市场适应性**: 不同的货币对可能需要调整d值阈值参数
5. **监控**: 建议定期监控EA的运行状态和交易结果

## 功能特点

✅ **完全按需求开发**: 严格按照您提供的策略需求实现
✅ **参数可配置**: 所有关键参数都可以调整
✅ **智能入场**: 基于d值自动确定最佳入场档位
✅ **严格条件**: 实现了复杂的K线形态判断条件
✅ **风险控制**: 自动设置止损，可选移动止损
✅ **持仓保护**: 持仓期间锁定ZigZag分析
✅ **信息显示**: 实时显示EA运行状态和关键信息
✅ **提前入场**: 可选的提前入场功能提高成功率

这个EA完全实现了您需求文档中的所有功能，可以直接用于回测和实盘交易。

//+------------------------------------------------------------------+
//|                                         ZigZag回调测试版.mq4   |
//|                                          Copyright 2024           |
//|                              简化版本，用于快速测试核心功能      |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024"
#property link      ""
#property version   "1.00"
#property strict

//--- 输入参数（简化版）
input int    ZigZag_Depth = 12;        // ZigZag深度
input int    ZigZag_Deviation = 5;     // ZigZag偏差
input int    ZigZag_Backstep = 3;      // ZigZag回溯
input int    StartBar = 4;             // 从第几根K线开始查找转折点

input bool   EnableTrading = false;    // 启用自动交易（默认关闭，仅显示分析）
input double LotSize = 0.1;            // 交易手数
input int    MagicNumber = 12345;      // 魔术编号

input bool   ShowInfo = true;          // 显示信息
input color  InfoColor = clrYellow;    // 信息颜色
input int    FontSize = 10;            // 字体大小

//--- 全局变量
double fiboLevels[8] = {0.0, 0.236, 0.382, 0.5, 0.618, 0.786, 0.876, 1.0};
datetime lastTurningPoints[3];
double lastTurningPrices[3];
string trendDirection = "";
double d1, d2, d;
int dSource;
double startPrice, endPrice;
int initialLevel = -1;
datetime lastBarTime = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // 初始化转折点数据
   for(int i = 0; i < 3; i++)
   {
      lastTurningPoints[i] = 0;
      lastTurningPrices[i] = 0;
   }
   
   Print("ZigZag回调测试版EA已启动");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   ObjectsDeleteAll(0, "ZigZag_");
   Print("ZigZag回调测试版EA已停止");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // 检查是否有新K线
   bool isNewBar = (Time[0] != lastBarTime);
   if(isNewBar)
   {
      lastBarTime = Time[0];
      
      // 查找转折点和分析趋势
      FindZigZagTurningPoints();
      AnalyzeTrend();
      DetermineInitialLevel();
      
      // 显示分析结果
      if(ShowInfo)
      {
         DisplayAnalysis();
      }
      
      // 如果启用交易，检查交易条件
      if(EnableTrading && d > 5.0)
      {
         CheckSimpleTrading();
      }
   }
}

//+------------------------------------------------------------------+
//| 查找ZigZag转折点                                                 |
//+------------------------------------------------------------------+
void FindZigZagTurningPoints()
{
   int foundPoints = 0;
   int shift = StartBar - 1;
   
   // 清除旧数据
   for(int i = 0; i < 3; i++)
   {
      lastTurningPoints[i] = 0;
      lastTurningPrices[i] = 0;
   }
   
   // 查找转折点
   while(foundPoints < 3 && shift < 500)
   {
      double zigzagValue = iCustom(NULL, 0, "ZigZag", ZigZag_Depth, ZigZag_Deviation, ZigZag_Backstep, 0, shift);
      
      if(zigzagValue != 0 && zigzagValue != EMPTY_VALUE)
      {
         lastTurningPoints[foundPoints] = Time[shift];
         lastTurningPrices[foundPoints] = zigzagValue;
         foundPoints++;
      }
      shift++;
   }
   
   if(foundPoints < 3)
   {
      Print("警告：只找到", foundPoints, "个ZigZag转折点");
   }
}

//+------------------------------------------------------------------+
//| 分析趋势                                                         |
//+------------------------------------------------------------------+
void AnalyzeTrend()
{
   // 确保有足够的转折点
   if(lastTurningPoints[0] == 0 || lastTurningPoints[1] == 0 || lastTurningPoints[2] == 0)
   {
      trendDirection = "无法判断趋势";
      return;
   }
   
   // 计算差值
   d1 = MathAbs(lastTurningPrices[0] - lastTurningPrices[1]);
   d2 = MathAbs(lastTurningPrices[1] - lastTurningPrices[2]);
   
   // 确定主要波动幅度
   d = MathMax(d1, d2);
   
   // 判断趋势方向
   if(d == d1) // d来自z1-z2
   {
      dSource = 1;
      startPrice = lastTurningPrices[1]; // z2为起点
      endPrice = lastTurningPrices[0];   // z1为终点
      
      if(lastTurningPrices[0] > lastTurningPrices[1])
         trendDirection = "看涨趋势";
      else
         trendDirection = "看跌趋势";
   }
   else // d来自z2-z3
   {
      dSource = 2;
      startPrice = lastTurningPrices[2]; // z3为起点
      endPrice = lastTurningPrices[1];   // z2为终点
      
      if(lastTurningPrices[1] > lastTurningPrices[2])
         trendDirection = "看涨趋势";
      else
         trendDirection = "看跌趋势";
   }
}

//+------------------------------------------------------------------+
//| 确定初始下单档位                                                 |
//+------------------------------------------------------------------+
void DetermineInitialLevel()
{
   if(d > 40.0)
      initialLevel = 1; // 第2档
   else if(d > 30.0)
      initialLevel = 2; // 第3档
   else if(d > 20.0)
      initialLevel = 3; // 第4档
   else if(d > 15.0)
      initialLevel = 4; // 第5档
   else if(d > 5.0)
      initialLevel = 5; // 第6档
   else
      initialLevel = -1; // 不交易
}

//+------------------------------------------------------------------+
//| 显示分析结果                                                     |
//+------------------------------------------------------------------+
void DisplayAnalysis()
{
   // 清除旧的信息对象
   ObjectsDeleteAll(0, "ZigZag_");
   
   // 创建信息标签
   string infoText = "=== ZigZag回调分析 ===\n";
   infoText += "趋势方向：" + trendDirection + "\n";
   infoText += "z1：" + DoubleToString(lastTurningPrices[0], Digits) + "\n";
   infoText += "z2：" + DoubleToString(lastTurningPrices[1], Digits) + "\n";
   infoText += "z3：" + DoubleToString(lastTurningPrices[2], Digits) + "\n";
   infoText += "d1=" + DoubleToString(d1, 1) + " d2=" + DoubleToString(d2, 1) + "\n";
   infoText += "d=" + DoubleToString(d, 1) + " (来源:d" + IntegerToString(dSource) + ")\n";
   infoText += "初始档位：" + (initialLevel >= 0 ? "第" + IntegerToString(initialLevel + 1) + "档" : "无") + "\n";
   
   if(initialLevel >= 0)
   {
      double range = MathAbs(endPrice - startPrice);
      double targetPrice;
      
      if(trendDirection == "看涨趋势")
         targetPrice = endPrice - range * fiboLevels[initialLevel];
      else
         targetPrice = endPrice + range * fiboLevels[initialLevel];
         
      infoText += "目标价格：" + DoubleToString(targetPrice, Digits) + "\n";
      infoText += "当前价格：" + DoubleToString(Close[0], Digits) + "\n";
      
      // 计算距离目标价格的差距
      double priceDiff = MathAbs(Close[0] - targetPrice);
      infoText += "价格差距：" + DoubleToString(priceDiff, Digits) + "\n";
   }
   
   infoText += "交易状态：" + (EnableTrading ? "已启用" : "仅分析") + "\n";
   
   // 创建标签对象
   if(ObjectCreate(0, "ZigZag_Info", OBJ_LABEL, 0, 0, 0))
   {
      ObjectSetString(0, "ZigZag_Info", OBJPROP_TEXT, infoText);
      ObjectSetInteger(0, "ZigZag_Info", OBJPROP_CORNER, CORNER_LEFT_UPPER);
      ObjectSetInteger(0, "ZigZag_Info", OBJPROP_XDISTANCE, 10);
      ObjectSetInteger(0, "ZigZag_Info", OBJPROP_YDISTANCE, 20);
      ObjectSetInteger(0, "ZigZag_Info", OBJPROP_FONTSIZE, FontSize);
      ObjectSetInteger(0, "ZigZag_Info", OBJPROP_COLOR, InfoColor);
   }
   
   ChartRedraw(0);
}

//+------------------------------------------------------------------+
//| 简单交易检查（测试版）                                           |
//+------------------------------------------------------------------+
void CheckSimpleTrading()
{
   if(initialLevel < 0)
      return;
      
   // 检查是否已有持仓
   bool hasPosition = false;
   for(int i = OrdersTotal() - 1; i >= 0; i--)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
         {
            hasPosition = true;
            break;
         }
      }
   }
   
   if(hasPosition)
      return; // 已有持仓，不再开新仓
   
   // 计算目标价格
   double range = MathAbs(endPrice - startPrice);
   double targetPrice;
   
   if(trendDirection == "看涨趋势")
      targetPrice = endPrice - range * fiboLevels[initialLevel];
   else
      targetPrice = endPrice + range * fiboLevels[initialLevel];
   
   // 简单的价格触及检查
   double tolerance = Point * 20;
   bool priceReached = false;
   
   if(trendDirection == "看涨趋势")
      priceReached = (Ask <= targetPrice + tolerance);
   else
      priceReached = (Bid >= targetPrice - tolerance);
   
   if(priceReached)
   {
      Print("价格接近目标位置，可考虑入场。目标：", targetPrice, " 当前：", (trendDirection == "看涨趋势" ? Ask : Bid));
      // 这里可以添加实际的下单逻辑
   }
}

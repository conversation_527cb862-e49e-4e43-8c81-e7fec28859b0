//+------------------------------------------------------------------+
//|                                           ZigZag回调交易EA.mq4 |
//|                                          Copyright 2024           |
//|                                   基于ZigZag转折点的回调交易EA    |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024"
#property link      ""
#property version   "1.00"
#property strict

//--- 输入参数
// ZigZag指标参数
input int    ZigZag_Depth = 12;        // ZigZag深度
input int    ZigZag_Deviation = 5;     // ZigZag偏差
input int    ZigZag_Backstep = 3;      // ZigZag回溯
input int    StartBar = 4;             // 从第几根K线开始查找转折点

// 斐波那契档位设置（8个档位）
input double Fibo_Level1 = 0.0;        // 第1档：起点
input double Fibo_Level2 = 0.236;      // 第2档：黄金分割
input double Fibo_Level3 = 0.382;      // 第3档
input double Fibo_Level4 = 0.5;        // 第4档：中点
input double Fibo_Level5 = 0.618;      // 第5档：黄金分割
input double Fibo_Level6 = 0.786;      // 第6档
input double Fibo_Level7 = 0.876;      // 第7档
input double Fibo_Level8 = 1.0;        // 第8档：终点

// 交易参数
input bool   EnableTrading = true;     // 启用自动交易
input double LotSize = 0.1;            // 交易手数
input int    MagicNumber = 12345;      // 魔术编号
input int    Slippage = 3;             // 滑点

// d值阈值设置（用于确定初始下单档位）
input double D_Threshold_Level2 = 40.0;  // d>40时从第2档开始
input double D_Threshold_Level3 = 30.0;  // 30<d≤40时从第3档开始
input double D_Threshold_Level4 = 20.0;  // 20<d≤30时从第4档开始
input double D_Threshold_Level5 = 15.0;  // 15<d≤20时从第5档开始
input double D_Threshold_Level6 = 5.0;   // 5<d≤15时从第6档开始
input double D_Min_Trade = 5.0;          // d≤5时不交易

// 提前入场功能
input bool   EnableEarlyEntry = true;   // 启用提前入场
input double EarlyEntryPercent = 3.0;   // 提前入场百分比（3%）

// 移动止损功能
input bool   EnableTrailingStop = false;  // 启用移动止损
input double TrailingMultiplier = 1.0;    // 移动止损倍数
input int    TrailingStopPoints = 44;     // 移动止损点数

// 显示参数
input bool   ShowInfo = true;           // 显示信息
input color  InfoColor = clrYellow;     // 信息颜色
input int    FontSize = 10;             // 字体大小

//--- 全局变量
double fiboLevels[8];                   // 斐波那契档位数组
datetime lastTurningPoints[3];          // 最近三个转折点时间
double lastTurningPrices[3];            // 最近三个转折点价格
string trendDirection = "";             // 趋势方向
double d1, d2, d;                       // 差值计算
int dSource;                            // d值来源（1=d1, 2=d2）
double startPrice, endPrice;            // 斐波那契起点和终点价格
int initialLevel = -1;                  // 初始下单档位
bool hasPosition = false;               // 是否有持仓
bool zigzagLocked = false;              // ZigZag是否锁定（持仓期间）
datetime lastBarTime = 0;               // 上一根K线时间

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // 初始化斐波那契档位数组
   fiboLevels[0] = Fibo_Level1;
   fiboLevels[1] = Fibo_Level2;
   fiboLevels[2] = Fibo_Level3;
   fiboLevels[3] = Fibo_Level4;
   fiboLevels[4] = Fibo_Level5;
   fiboLevels[5] = Fibo_Level6;
   fiboLevels[6] = Fibo_Level7;
   fiboLevels[7] = Fibo_Level8;
   
   // 验证斐波那契档位是否按升序排列
   for(int i = 0; i < 7; i++)
   {
      if(fiboLevels[i] >= fiboLevels[i+1])
      {
         Print("错误：斐波那契档位必须按升序排列！");
         return(INIT_PARAMETERS_INCORRECT);
      }
   }
   
   // 初始化转折点数据
   for(int i = 0; i < 3; i++)
   {
      lastTurningPoints[i] = 0;
      lastTurningPrices[i] = 0;
   }
   
   // 初始查找转折点
   FindZigZagTurningPoints();
   AnalyzeTrend();
   DetermineInitialLevel();
   
   Print("ZigZag回调交易EA已启动");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 清除图表对象
   ObjectsDeleteAll(0, "ZigZag_");
   Print("ZigZag回调交易EA已停止");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // 检查是否有新K线
   bool isNewBar = (Time[0] != lastBarTime);
   if(isNewBar)
   {
      lastBarTime = Time[0];
      
      // 检查持仓状态
      CheckPositionStatus();
      
      // 如果没有持仓，更新ZigZag转折点
      if(!hasPosition && !zigzagLocked)
      {
         FindZigZagTurningPoints();
         AnalyzeTrend();
         DetermineInitialLevel();
      }
      
      // 处理移动止损
      if(EnableTrailingStop && hasPosition)
      {
         ManageTrailingStop();
      }
   }
   
   // 处理交易逻辑
   if(EnableTrading && d > D_Min_Trade && !hasPosition)
   {
      CheckTradingConditions();
   }
   
   // 显示信息
   if(ShowInfo)
   {
      DisplayInfo();
   }
}

//+------------------------------------------------------------------+
//| 查找ZigZag转折点                                                 |
//+------------------------------------------------------------------+
void FindZigZagTurningPoints()
{
   int foundPoints = 0;
   int shift = StartBar - 1;
   
   // 清除旧数据
   for(int i = 0; i < 3; i++)
   {
      lastTurningPoints[i] = 0;
      lastTurningPrices[i] = 0;
   }
   
   // 查找转折点
   while(foundPoints < 3 && shift < 1000)
   {
      double zigzagValue = iCustom(NULL, 0, "ZigZag", ZigZag_Depth, ZigZag_Deviation, ZigZag_Backstep, 0, shift);
      
      if(zigzagValue != 0 && zigzagValue != EMPTY_VALUE)
      {
         lastTurningPoints[foundPoints] = Time[shift];
         lastTurningPrices[foundPoints] = zigzagValue;
         foundPoints++;
      }
      shift++;
   }
   
   if(foundPoints < 3)
   {
      Print("警告：只找到", foundPoints, "个ZigZag转折点");
   }
}

//+------------------------------------------------------------------+
//| 分析趋势                                                         |
//+------------------------------------------------------------------+
void AnalyzeTrend()
{
   // 确保有足够的转折点
   if(lastTurningPoints[0] == 0 || lastTurningPoints[1] == 0 || lastTurningPoints[2] == 0)
   {
      trendDirection = "无法判断趋势";
      return;
   }
   
   // 计算差值
   d1 = MathAbs(lastTurningPrices[0] - lastTurningPrices[1]);
   d2 = MathAbs(lastTurningPrices[1] - lastTurningPrices[2]);
   
   // 确定主要波动幅度
   d = MathMax(d1, d2);
   
   // 判断趋势方向
   if(d == d1) // d来自z1-z2
   {
      dSource = 1;
      startPrice = lastTurningPrices[1]; // z2为起点
      endPrice = lastTurningPrices[0];   // z1为终点
      
      if(lastTurningPrices[0] > lastTurningPrices[1])
         trendDirection = "看涨趋势";
      else
         trendDirection = "看跌趋势";
   }
   else // d来自z2-z3
   {
      dSource = 2;
      startPrice = lastTurningPrices[2]; // z3为起点
      endPrice = lastTurningPrices[1];   // z2为终点
      
      if(lastTurningPrices[1] > lastTurningPrices[2])
         trendDirection = "看涨趋势";
      else
         trendDirection = "看跌趋势";
   }
}

//+------------------------------------------------------------------+
//| 确定初始下单档位                                                 |
//+------------------------------------------------------------------+
void DetermineInitialLevel()
{
   if(d > D_Threshold_Level2)
      initialLevel = 1; // 第2档
   else if(d > D_Threshold_Level3)
      initialLevel = 2; // 第3档
   else if(d > D_Threshold_Level4)
      initialLevel = 3; // 第4档
   else if(d > D_Threshold_Level5)
      initialLevel = 4; // 第5档
   else if(d > D_Threshold_Level6)
      initialLevel = 5; // 第6档
   else
      initialLevel = -1; // 不交易
}

//+------------------------------------------------------------------+
//| 检查持仓状态                                                     |
//+------------------------------------------------------------------+
void CheckPositionStatus()
{
   hasPosition = false;

   for(int i = OrdersTotal() - 1; i >= 0; i--)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
         {
            hasPosition = true;
            zigzagLocked = true; // 持仓期间锁定ZigZag
            return;
         }
      }
   }

   // 如果没有持仓，解锁ZigZag
   if(!hasPosition && zigzagLocked)
   {
      zigzagLocked = false;
      Print("平仓后更新ZigZag转折点");
   }
}

//+------------------------------------------------------------------+
//| 检查交易条件                                                     |
//+------------------------------------------------------------------+
void CheckTradingConditions()
{
   if(initialLevel < 0 || trendDirection == "无法判断趋势")
      return;

   // 获取当前价格
   double currentPrice = (trendDirection == "看涨趋势") ? Ask : Bid;

   // 计算斐波那契回调价格
   double range = MathAbs(endPrice - startPrice);
   double fiboPrice;

   if(trendDirection == "看涨趋势")
   {
      // 上升趋势：回调价格 = 终点价格 - d × 斐波那契比例
      fiboPrice = endPrice - range * fiboLevels[initialLevel];
   }
   else
   {
      // 下降趋势：回调价格 = 终点价格 + d × 斐波那契比例
      fiboPrice = endPrice + range * fiboLevels[initialLevel];
   }

   // 提前入场功能
   if(EnableEarlyEntry)
   {
      double earlyDistance = range * (EarlyEntryPercent / 100.0);
      if(trendDirection == "看涨趋势")
         fiboPrice += earlyDistance; // 在完全回调到位前3%入场
      else
         fiboPrice -= earlyDistance;
   }

   // 检查下单条件
   if(CheckEntryConditions(fiboPrice))
   {
      PlaceOrder();
   }
}

//+------------------------------------------------------------------+
//| 检查入场条件                                                     |
//+------------------------------------------------------------------+
bool CheckEntryConditions(double fiboPrice)
{
   // 检查价格是否触及斐波那契档位
   bool priceReached = false;
   double tolerance = Point * 10; // 10点容差

   if(trendDirection == "看涨趋势")
   {
      // 看涨趋势：检查是否回调到位
      priceReached = (Ask <= fiboPrice + tolerance);

      // 检查K线条件：上上上柱或上上柱最低点小于斐波那契价格
      // 上上柱为阳线，上柱为阳线，上上上柱为阴线
      if(priceReached)
      {
         bool condition1 = (Low[3] < fiboPrice || Low[2] < fiboPrice); // 上上上柱或上上柱最低点
         bool condition2 = (Close[2] > Open[2]); // 上上柱为阳线
         bool condition3 = (Close[1] > Open[1]); // 上柱为阳线
         bool condition4 = (Close[3] < Open[3]); // 上上上柱为阴线

         return (condition1 && condition2 && condition3 && condition4);
      }
   }
   else if(trendDirection == "看跌趋势")
   {
      // 看跌趋势：检查是否反弹到位
      priceReached = (Bid >= fiboPrice - tolerance);

      // 检查K线条件：上上上柱或上上柱最高点大于斐波那契价格
      // 上上柱为阴线，上柱为阴线，上上上柱为阳线
      if(priceReached)
      {
         bool condition1 = (High[3] > fiboPrice || High[2] > fiboPrice); // 上上上柱或上上柱最高点
         bool condition2 = (Close[2] < Open[2]); // 上上柱为阴线
         bool condition3 = (Close[1] < Open[1]); // 上柱为阴线
         bool condition4 = (Close[3] > Open[3]); // 上上上柱为阳线

         return (condition1 && condition2 && condition3 && condition4);
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| 下单函数                                                         |
//+------------------------------------------------------------------+
void PlaceOrder()
{
   int orderType;
   double price;
   double stopLoss = 0;
   string comment;

   // 计算止损价格（前4柱的最低点/最高点）
   if(trendDirection == "看涨趋势")
   {
      orderType = OP_BUY;
      price = Ask;
      comment = "ZigZag看涨-档位" + IntegerToString(initialLevel + 1);

      // 止损为前4柱最低点
      stopLoss = Low[1];
      for(int i = 2; i <= 4; i++)
      {
         if(Low[i] < stopLoss)
            stopLoss = Low[i];
      }
   }
   else
   {
      orderType = OP_SELL;
      price = Bid;
      comment = "ZigZag看跌-档位" + IntegerToString(initialLevel + 1);

      // 止损为前4柱最高点
      stopLoss = High[1];
      for(int i = 2; i <= 4; i++)
      {
         if(High[i] > stopLoss)
            stopLoss = High[i];
      }
   }

   // 执行下单
   int ticket = OrderSend(Symbol(), orderType, LotSize, price, Slippage, stopLoss, 0, comment, MagicNumber, 0, clrNONE);

   if(ticket > 0)
   {
      Print("下单成功：", comment, " 价格：", price, " 止损：", stopLoss);
      hasPosition = true;
      zigzagLocked = true;
   }
   else
   {
      Print("下单失败，错误代码：", GetLastError());
   }
}

//+------------------------------------------------------------------+
//| 移动止损管理                                                     |
//+------------------------------------------------------------------+
void ManageTrailingStop()
{
   for(int i = OrdersTotal() - 1; i >= 0; i--)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
         {
            double profitThreshold = TrailingMultiplier * 0.1 * d * 100 * Point;
            double newStopLoss = 0;
            bool shouldModify = false;

            if(OrderType() == OP_BUY)
            {
               // 多单：检查上一柱最低点是否盈利达到阈值
               double lastLow = Low[1];
               double currentProfit = (lastLow - OrderOpenPrice()) / Point;

               if(currentProfit >= profitThreshold / Point)
               {
                  newStopLoss = lastLow - TrailingStopPoints * Point;

                  // 确保新止损比当前止损更有利
                  if(newStopLoss > OrderStopLoss() + Point)
                     shouldModify = true;
               }
            }
            else if(OrderType() == OP_SELL)
            {
               // 空单：检查上一柱最高点是否盈利达到阈值
               double lastHigh = High[1];
               double currentProfit = (OrderOpenPrice() - lastHigh) / Point;

               if(currentProfit >= profitThreshold / Point)
               {
                  newStopLoss = lastHigh + TrailingStopPoints * Point;

                  // 确保新止损比当前止损更有利
                  if(newStopLoss < OrderStopLoss() - Point || OrderStopLoss() == 0)
                     shouldModify = true;
               }
            }

            // 修改止损
            if(shouldModify)
            {
               bool result = OrderModify(OrderTicket(), OrderOpenPrice(), newStopLoss, OrderTakeProfit(), 0, clrNONE);
               if(result)
               {
                  Print("移动止损成功：订单", OrderTicket(), " 新止损：", newStopLoss);
               }
               else
               {
                  Print("移动止损失败：", GetLastError());
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 显示信息                                                         |
//+------------------------------------------------------------------+
void DisplayInfo()
{
   // 清除旧的信息对象
   ObjectsDeleteAll(0, "ZigZag_Info");

   // 创建信息标签
   string infoText = "=== ZigZag回调交易EA ===\n";
   infoText += "趋势方向：" + trendDirection + "\n";
   infoText += "z1：" + DoubleToString(lastTurningPrices[0], Digits) + "\n";
   infoText += "z2：" + DoubleToString(lastTurningPrices[1], Digits) + "\n";
   infoText += "z3：" + DoubleToString(lastTurningPrices[2], Digits) + "\n";
   infoText += "d1=" + DoubleToString(d1, 1) + " d2=" + DoubleToString(d2, 1) + "\n";
   infoText += "d=" + DoubleToString(d, 1) + " (来源:d" + IntegerToString(dSource) + ")\n";
   infoText += "初始档位：" + (initialLevel >= 0 ? "第" + IntegerToString(initialLevel + 1) + "档" : "无") + "\n";
   infoText += "持仓状态：" + (hasPosition ? "有持仓" : "无持仓") + "\n";
   infoText += "ZigZag状态：" + (zigzagLocked ? "锁定" : "更新中") + "\n";

   if(initialLevel >= 0)
   {
      double range = MathAbs(endPrice - startPrice);
      double targetPrice;

      if(trendDirection == "看涨趋势")
         targetPrice = endPrice - range * fiboLevels[initialLevel];
      else
         targetPrice = endPrice + range * fiboLevels[initialLevel];

      infoText += "目标价格：" + DoubleToString(targetPrice, Digits) + "\n";
   }

   // 创建标签对象
   if(ObjectCreate(0, "ZigZag_Info", OBJ_LABEL, 0, 0, 0))
   {
      ObjectSetString(0, "ZigZag_Info", OBJPROP_TEXT, infoText);
      ObjectSetInteger(0, "ZigZag_Info", OBJPROP_CORNER, CORNER_LEFT_UPPER);
      ObjectSetInteger(0, "ZigZag_Info", OBJPROP_XDISTANCE, 10);
      ObjectSetInteger(0, "ZigZag_Info", OBJPROP_YDISTANCE, 20);
      ObjectSetInteger(0, "ZigZag_Info", OBJPROP_FONTSIZE, FontSize);
      ObjectSetInteger(0, "ZigZag_Info", OBJPROP_COLOR, InfoColor);
   }

   ChartRedraw(0);
}

# ZigZag回调交易EA参数配置模板

## 基础配置（推荐设置）

### ZigZag指标参数
ZigZag_Depth=12
ZigZag_Deviation=5
ZigZag_Backstep=3
StartBar=4

### 斐波那契档位设置（标准黄金分割）
Fibo_Level1=0.0
Fibo_Level2=0.236
Fibo_Level3=0.382
Fibo_Level4=0.5
Fibo_Level5=0.618
Fibo_Level6=0.786
Fibo_Level7=0.876
Fibo_Level8=1.0

### 交易参数
EnableTrading=true
LotSize=0.1
MagicNumber=12345
Slippage=3

### d值阈值设置（标准配置）
D_Threshold_Level2=40.0
D_Threshold_Level3=30.0
D_Threshold_Level4=20.0
D_Threshold_Level5=15.0
D_Threshold_Level6=5.0
D_Min_Trade=5.0

### 功能开关
EnableEarlyEntry=true
EarlyEntryPercent=3.0
EnableTrailingStop=false
TrailingMultiplier=1.0
TrailingStopPoints=44

## 保守配置（低风险）

### d值阈值设置（保守）
D_Threshold_Level2=50.0
D_Threshold_Level3=40.0
D_Threshold_Level4=30.0
D_Threshold_Level5=20.0
D_Threshold_Level6=10.0
D_Min_Trade=10.0

### 功能设置
EnableEarlyEntry=false
EnableTrailingStop=true
TrailingMultiplier=0.8
TrailingStopPoints=30

## 激进配置（高频交易）

### d值阈值设置（激进）
D_Threshold_Level2=30.0
D_Threshold_Level3=20.0
D_Threshold_Level4=15.0
D_Threshold_Level5=10.0
D_Threshold_Level6=3.0
D_Min_Trade=3.0

### 功能设置
EnableEarlyEntry=true
EarlyEntryPercent=5.0
EnableTrailingStop=true
TrailingMultiplier=1.2
TrailingStopPoints=50

## 不同货币对建议配置

### 主要货币对（EURUSD, GBPUSD, USDJPY等）
ZigZag_Depth=12
ZigZag_Deviation=5
D_Threshold_Level2=40.0
D_Min_Trade=5.0

### 波动性较大的货币对（GBPJPY, EURJPY等）
ZigZag_Depth=15
ZigZag_Deviation=8
D_Threshold_Level2=60.0
D_Min_Trade=8.0

### 波动性较小的货币对（EURCHF, USDCHF等）
ZigZag_Depth=10
ZigZag_Deviation=3
D_Threshold_Level2=25.0
D_Min_Trade=3.0

## 不同时间周期建议

### M15时间周期
ZigZag_Depth=8
ZigZag_Deviation=3
D_Min_Trade=3.0
EarlyEntryPercent=2.0

### H1时间周期
ZigZag_Depth=12
ZigZag_Deviation=5
D_Min_Trade=5.0
EarlyEntryPercent=3.0

### H4时间周期
ZigZag_Depth=15
ZigZag_Deviation=8
D_Min_Trade=8.0
EarlyEntryPercent=4.0

### D1时间周期
ZigZag_Depth=20
ZigZag_Deviation=10
D_Min_Trade=15.0
EarlyEntryPercent=5.0

## 回测优化建议

### 第一步：基础参数优化
1. 先固定其他参数，只优化ZigZag_Depth（范围：8-20）
2. 然后优化ZigZag_Deviation（范围：3-10）
3. 最后优化StartBar（范围：3-6）

### 第二步：d值阈值优化
1. 根据历史数据统计d值分布
2. 调整各档位阈值，确保交易频率适中
3. 重点优化D_Min_Trade，避免过度交易

### 第三步：功能参数优化
1. 测试EnableEarlyEntry的效果
2. 优化EarlyEntryPercent（范围：1-5%）
3. 测试移动止损参数

### 第四步：风险管理优化
1. 根据最大回撤调整LotSize
2. 测试不同的TrailingStopPoints
3. 评估整体风险收益比

## 注意事项

1. **参数过度优化风险**：避免过度拟合历史数据
2. **样本外测试**：保留部分数据用于验证
3. **不同市场环境**：在趋势市和震荡市分别测试
4. **滑点和点差**：回测时考虑实际交易成本
5. **资金管理**：根据账户资金调整手数

## 性能评估指标

### 盈利能力指标
- 总收益率
- 年化收益率
- 夏普比率
- 盈利因子

### 风险控制指标
- 最大回撤
- 连续亏损次数
- 胜率
- 平均盈亏比

### 交易频率指标
- 总交易次数
- 月均交易次数
- 持仓时间分布

建议在不同的市场环境和时间周期下进行充分的回测，找到最适合您交易风格的参数组合。
